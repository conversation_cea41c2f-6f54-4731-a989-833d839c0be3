import 'package:flutter/material.dart';

class AudioRoomWrapperAnimationController {
  static const double miniPlayerSize = 48.0;
  static const double maxRadius = 24.0;
  static const Duration animationDuration = Duration(milliseconds: 400);

  final TickerProvider vsync;
  late final AnimationController minimizeController;
  late final AnimationController closeController;
  late Animation<RelativeRect> positionAnimation;
  late Animation<Offset> slideAnimation;
  bool isAnimationInitialized = false;

  AudioRoomWrapperAnimationController({required this.vsync}) {
    _initializeControllers();
  }

  void _initializeControllers() {
    minimizeController = AnimationController(
      duration: animationDuration,
      vsync: vsync,
    );

    closeController = AnimationController(
      duration: animationDuration,
      vsync: vsync,
    );

    slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: closeController,
      curve: Curves.easeOutCubic,
      reverseCurve: Curves.easeInCubic,
    ));

    _initializeDefaultPositionAnimation();
  }

  void _initializeDefaultPositionAnimation() {
    positionAnimation = RelativeRectTween(
      begin: const RelativeRect.fromLTRB(0, 0, 0, 0),
      end: const RelativeRect.fromLTRB(0, 0, 0, 0),
    ).animate(minimizeController);
  }

  void initializePositionAnimation(Size screenSize, Offset miniPlayerPosition) {
    // 重新初始化动画，确保每次都使用最新的位置
    isAnimationInitialized = false;

    // begin: 小球位置 (minimizeController.value = 0.0)
    // end: 全屏位置 (minimizeController.value = 1.0)
    positionAnimation = RelativeRectTween(
      begin: RelativeRect.fromLTRB(
        miniPlayerPosition.dx,
        miniPlayerPosition.dy,
        screenSize.width - miniPlayerPosition.dx - miniPlayerSize,
        screenSize.height - miniPlayerPosition.dy - miniPlayerSize,
      ),
      end: const RelativeRect.fromLTRB(0, 0, 0, 0),
    ).animate(CurvedAnimation(
      parent: minimizeController,
      curve: Curves.easeOutBack, // 展开时使用弹性效果
      reverseCurve: Curves.easeInCubic, // 收缩时使用平滑效果
    ));

    isAnimationInitialized = true;
  }

  void dispose() {
    minimizeController.dispose();
    closeController.dispose();
  }
}
