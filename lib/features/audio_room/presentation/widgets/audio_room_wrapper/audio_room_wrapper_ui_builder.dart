import 'package:flutter/material.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_wrapper/audio_room_wrapper_animation_controller.dart';

class AudioRoomWrapperUIBuilder {
  static Widget buildAnimatedContent(
    BuildContext context,
    AudioRoomWrapperAnimationController controller,
    Widget Function(double radius) contentBuilder,
  ) {
    // 使用与位置动画相同的曲线，确保同步
    final animationValue = controller.minimizeController.value;
    final curvedValue = Curves.easeOutBack.transform(1 - animationValue);
    final radius = AudioRoomWrapperAnimationController.maxRadius * curvedValue;
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        PositionedTransition(
          rect: controller.positionAnimation,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(radius),
            child: SizedBox(
              width: AudioRoomWrapperAnimationController.miniPlayerSize +
                  (animationValue *
                      (screenSize.width -
                          AudioRoomWrapperAnimationController.miniPlayerSize)),
              height: AudioRoomWrapperAnimationController.miniPlayerSize +
                  (animationValue *
                      (screenSize.height -
                          AudioRoomWrapperAnimationController.miniPlayerSize)),
              child: OverflowBox(
                alignment: Alignment.center,
                maxWidth: screenSize.width,
                maxHeight: screenSize.height,
                child: contentBuilder(radius),
              ),
            ),
          ),
        ),
      ],
    );
  }

  static Widget buildContainer(
    BuildContext context,
    AudioRoomWrapperAnimationController controller,
    Widget child,
    double radius,
  ) {
    final double value = controller.minimizeController.value;
    // Define the start and end colors for the gradient
    const List<Color> startColors = [
      Colors.black,
      Colors.black,
      Colors.black,
    ];
    const List<Color> endColors = [
      Color(0xFF111111),
      Color(0xFF292929),
      Color(0xFF373737),
    ];
    // Interpolate each color in the gradient
    final List<Color> lerpedColors = List<Color>.generate(
      startColors.length,
      (int i) => Color.lerp(
        startColors[i].withValues(alpha: 0), // fully transparent black
        endColors[i],
        value,
      )!,
    );
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: lerpedColors,
          stops: const [0.19, 0.84, 1.0],
        ),
        borderRadius: BorderRadius.circular(radius),
      ),
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: buildScaffold(controller, child),
    );
  }

  static Widget buildScaffold(
    AudioRoomWrapperAnimationController controller,
    Widget child,
  ) {
    // 改进透明度动画：只在接近全屏时才显示内容，让最小化动画更干净
    final animationValue = controller.minimizeController.value;

    // 当动画值小于 0.7 时完全隐藏内容，大于 0.7 时逐渐显示
    final opacity = animationValue > 0.7
        ? Curves.easeOut.transform((animationValue - 0.7) / 0.3).clamp(0.0, 1.0)
        : 0.0;

    return Opacity(
      opacity: opacity,
      child: child,
    );
  }
}
