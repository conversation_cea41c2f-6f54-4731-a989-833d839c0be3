import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/user_info_cache_service.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/extra_user_info_model.dart';

void main() {
  group('UserInfoCacheService', () {
    late UserInfoCacheService cacheService;

    setUp(() {
      cacheService = UserInfoCacheService();
    });

    group('LRU Cache', () {
      test('should store and retrieve cache entries', () {
        final cache = LRUCache<String, String>(maxSize: 3);
        
        cache.put('key1', 'value1', const Duration(minutes: 5));
        cache.put('key2', 'value2', const Duration(minutes: 5));
        
        final entry1 = cache.get('key1');
        final entry2 = cache.get('key2');
        
        expect(entry1?.data, equals('value1'));
        expect(entry2?.data, equals('value2'));
      });

      test('should evict oldest entry when max size exceeded', () {
        final cache = LRUCache<String, String>(maxSize: 2);
        
        cache.put('key1', 'value1', const Duration(minutes: 5));
        cache.put('key2', 'value2', const Duration(minutes: 5));
        cache.put('key3', 'value3', const Duration(minutes: 5));
        
        final entry1 = cache.get('key1'); // Should be null (evicted)
        final entry2 = cache.get('key2');
        final entry3 = cache.get('key3');
        
        expect(entry1, isNull);
        expect(entry2?.data, equals('value2'));
        expect(entry3?.data, equals('value3'));
      });

      test('should return null for expired entries', () {
        final cache = LRUCache<String, String>(maxSize: 3);
        
        cache.put('key1', 'value1', const Duration(milliseconds: 1));
        
        // Wait for expiration
        Future.delayed(const Duration(milliseconds: 10), () {
          final entry = cache.get('key1');
          expect(entry, isNull);
        });
      });
    });

    group('ExtraUserInfo Cache', () {
      test('should cache and retrieve extra user info', () {
        const userId = 'user123';
        const userInfo = ExtraUserInfoModel();
        
        cacheService.setExtraUserInfo(userId, userInfo);
        final cachedInfo = cacheService.getExtraUserInfo(userId);
        
        expect(cachedInfo, equals(userInfo));
      });

      test('should return null for non-existent user', () {
        final cachedInfo = cacheService.getExtraUserInfo('nonexistent');
        expect(cachedInfo, isNull);
      });

      test('should check if cache exists', () {
        const userId = 'user123';
        const userInfo = ExtraUserInfoModel();
        
        expect(cacheService.hasExtraUserInfoCache(userId), isFalse);
        
        cacheService.setExtraUserInfo(userId, userInfo);
        expect(cacheService.hasExtraUserInfoCache(userId), isTrue);
      });
    });

    group('UserInfo Cache', () {
      test('should cache and retrieve user info', () {
        const userId = 'user123';
        const userInfo = OtherUserInfoModel();
        
        cacheService.setUserInfo(userId, userInfo);
        final cachedInfo = cacheService.getUserInfo(userId);
        
        expect(cachedInfo, equals(userInfo));
      });

      test('should return null for non-existent user', () {
        final cachedInfo = cacheService.getUserInfo('nonexistent');
        expect(cachedInfo, isNull);
      });
    });

    group('Cache Management', () {
      test('should remove user cache', () {
        const userId = 'user123';
        const extraUserInfo = ExtraUserInfoModel();
        const userInfo = OtherUserInfoModel();
        
        cacheService.setExtraUserInfo(userId, extraUserInfo);
        cacheService.setUserInfo(userId, userInfo);
        
        expect(cacheService.getExtraUserInfo(userId), isNotNull);
        expect(cacheService.getUserInfo(userId), isNotNull);
        
        cacheService.removeUserCache(userId);
        
        expect(cacheService.getExtraUserInfo(userId), isNull);
        expect(cacheService.getUserInfo(userId), isNull);
      });

      test('should clear all cache', () {
        const userId1 = 'user123';
        const userId2 = 'user456';
        const userInfo = OtherUserInfoModel();
        
        cacheService.setUserInfo(userId1, userInfo);
        cacheService.setUserInfo(userId2, userInfo);
        
        expect(cacheService.getUserInfo(userId1), isNotNull);
        expect(cacheService.getUserInfo(userId2), isNotNull);
        
        cacheService.clearAllCache();
        
        expect(cacheService.getUserInfo(userId1), isNull);
        expect(cacheService.getUserInfo(userId2), isNull);
      });

      test('should provide cache stats', () {
        const userId = 'user123';
        const userInfo = OtherUserInfoModel();
        const extraUserInfo = ExtraUserInfoModel();
        
        cacheService.setUserInfo(userId, userInfo);
        cacheService.setExtraUserInfo(userId, extraUserInfo);
        
        final stats = cacheService.getCacheStats();
        
        expect(stats['userInfoCacheSize'], equals(1));
        expect(stats['extraUserInfoCacheSize'], equals(1));
        expect(stats['totalCacheSize'], equals(2));
        expect(stats['maxCacheSize'], equals(100));
        expect(stats['defaultTtlMinutes'], equals(8));
      });
    });
  });
}
